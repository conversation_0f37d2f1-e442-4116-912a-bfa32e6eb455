# Node modules
/node_modules/

# Build output
/dist/
/build/

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln

# OS files
.DS_Store
Thumbs.db

# Optional npm cache directory
.npm/

# Yarn Integrity file
.yarn-integrity

# Testing coverage
/coverage/

# Misc
*.log

# Mac system files
.DS_Store

# Optional lock files
package-lock.json
yarn.lock
