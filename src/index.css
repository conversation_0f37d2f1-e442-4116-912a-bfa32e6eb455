@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&display=swap');

@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&family=IBM+Plex+Sans:ital,wght@0,100..700;1,100..700&display=swap');


@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

/* Light Theme */
:root {
  --background: #fdfdfd;
  --navbar: #ffffff;
  --headline: #1c1c1f;
  --paragraph: #4b4b51;
  --button: #7E5AF0;
  --button-text: #ffffff;
  --button-hover: #274bc9;
  --button-secondary: #e9e9ee;
  --button-secondary-text: #1c1c1f;
  --button-secondary-hover: #d9d9e0;
  --stroke: #e0e0e0;
  --main: #1c1c1f;
  --highlight: #7f5af0;
  --secondary: #72757e;
  --tertiary: #2cb67d;
  --card: #ffffff;
  --card-border: #dcdcdc;
  --border: #dcdcdc;
  --card-headline: #1c1c1f;
  --card-paragraph: #4b4b51;
  --card-button: #7E5AF0;
  --card-button-text: #ffffff;
  --card-stroke: #e0e0e0;
  --card-main: #1c1c1f;
  --card-highlight: #7f5af0;
  --grid-line: #1c1c1f0a;
  --danger: #e5484d;
  --danger-text: #ffffff;
  --danger-hover: #c72f32;
  --radius: 12px
}

/* Dark Theme */
[data-theme='dark'] {
  --background: #1b1b1f;
  --navbar: #202127;
  --headline: #dfdfd6;
  --paragraph: #98989f;
  --button: #7E5AF0;
  --button-text: #ffff;
  --button-hover: #002cbd;
  --button-secondary: #32363f;
  --button-secondary-text: #dfdfd6;
  --button-secondary-hover: #414853;
  --button-text: #fffffe;
  --stroke: #010101;
  --main: #fffffe;
  --highlight: #7f5af0;
  --secondary: #72757e;
  --tertiary: #2cb67d;
  --card: #202127;
  --card-border: #fcfcfc34;
  --border: #fcfcfc39;
  --card-headline: #dfdfd6;
  --card-paragraph: #98989f;
  --card-button: #7E5AF0;
  --card-button-text: #fffffe;
  --card-stroke: #010101;
  --card-main: #fffffe;
  --card-highlight: #7f5af0;
  --highlight: #7f5af0;
  --grid-line: #80808019;
  --danger: #e5484d;
  --danger-text: #ffffff;
  --danger-hover: #c72f32;
  --radius: 12px
}

body {
  color: var(--headline);
  transition:
    background-color 0.3s,
    color 0.3s;
}

* {
  transition:
    background-color 0.3s,
    color 0.3s,
    border-color 0.3s;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  @apply text-[var(--headline)];
}

p {
  @apply text-[var(--paragraph)];
}

@layer base {
  * {
  font-family: "IBM Plex Sans", sans-serif !important;

    border-color: var(--border);
  }

  body {
    @apply bg-background text-paragraph;
  }

  .theme {
    --animate-shiny-text: shiny-text 8s infinite;
  }
}

/* Enhanced animations and transitions */
@layer utilities {
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }

    50% {
      background-position: 100% 50%;
    }

    100% {
      background-position: 0% 50%;
    }
  }

  .glass-effect {
    backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: var(--highlight);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--tertiary);
  }
}

.hoverd {
  transition: all 0.3s ease 0s !important;
}

button {
  border-radius: 4px !important;
}

/* ---break---
*/

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-paragraph;
  }
}

/* ---break---
*/

@theme inline {
  @keyframes shiny-text {

    0%,
    90%,
    100% {
      background-position: calc(-100% - var(--shiny-width)) 0;
    }

    30%,
    60% {
      background-position: calc(100% + var(--shiny-width)) 0;
    }
  }
}

/* Toast (cookie consent) styles */
.cookie-toast {
  position: fixed;
  right: 1.5rem;
  bottom: 1.5rem;
  z-index: 9999;
  min-width: 280px;
  max-width: 90vw;
  background: var(--card);
  color: var(--headline);
  border-radius: var(--radius);
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.12);
  padding: 1.25rem 1.5rem 1rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  animation: slideInUp 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(60px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cookie-toast-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.cookie-toast-desc {
  font-size: 0.95rem;
  color: var(--paragraph);
}

.cookie-toast-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.cookie-toast-btn {
  padding: 0.5rem 1.25rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.95rem;
  border: none;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}

.cookie-toast-btn.allow {
  background: var(--button);
  color: var(--button-text);
}

.cookie-toast-btn.deny {
  background: var(--button-secondary);
  color: var(--button-secondary-text);
}

@media (max-width: 600px) {
  .cookie-toast {
    right: 0.5rem;
    left: 0.5rem;
    bottom: 0.5rem;
    min-width: unset;
    max-width: unset;
    padding: 1rem 1rem 0.75rem 1rem;
  }
}
