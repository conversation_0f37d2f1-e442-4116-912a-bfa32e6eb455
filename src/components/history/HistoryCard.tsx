import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { AnalysisHistoryItem } from '@/lib/analysisStorage';
import { motion } from 'framer-motion';
import { Calendar, Trash2 } from 'lucide-react';
import React from 'react';

interface HistoryCardProps {
  item: AnalysisHistoryItem;
  isSelected: boolean;
  onToggleSelection: (id: string) => void;
  onDelete: (id: string) => void;
  onOpen: (item: AnalysisHistoryItem) => void;
  className?: string;
}

const HistoryCard: React.FC<HistoryCardProps> = ({
  item,
  isSelected,
  onToggleSelection,
  onDelete,
  onOpen,
  className = '',
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <motion.div
      className={`group ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <Card
        className={`w-full cursor-pointer transition-all duration-200 ${
          isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:shadow-md'
        }`}
        onClick={() => onOpen(item)}
      >
        <CardHeader className="pb-3 flex flex-row items-center justify-between gap-2">
          <Checkbox
            checked={isSelected}
            onClick={e => e.stopPropagation()}
            onCheckedChange={() => onToggleSelection(item.id)}
            className="mt-1"
          />
          <div className="flex-1 min-w-0">
            <div className="font-semibold text-[var(--headline)] truncate">
              {item.originalIdea.length > 120 ? item.originalIdea.slice(0, 120) + '...' : item.originalIdea}
            </div>
            <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(item.timestamp)}</span>
            </div>
          </div>
          <Badge className="font-bold ml-2">{item.overallScore}/100</Badge>
          <Button
            variant="destructive"
            size="icon"
            onClick={e => { e.stopPropagation(); onDelete(item.id); }}
            className="ml-2"
            aria-label="Delete analysis"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </CardHeader>
        <CardContent className="pt-0 pb-3 text-xs text-gray-600 truncate">
          {item.analysisResults.targetAudience}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default HistoryCard;
