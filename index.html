<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <link
      href="https://api.fontshare.com/v2/css?f[]=chillax@200,300,400,500,600,700,1&display=swap"
      rel="stylesheet"
    />
    <title>SaasCan - AI-Powered SaaS Idea Scanner & Analysis</title>
    <meta
      name="description"
      content="Scan and analyze your SaaS ideas with AI-powered insights. Support for English and Arabic with advanced business recommendations."
    />
    <meta name="author" content="saascan" />

    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="96x96" href="/favicon/favicon-96x96.png" />
    <link rel="icon" type="image/svg+xml" href="/favicon/favicon.svg" />
    <link rel="shortcut icon" href="/favicon/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/favicon/apple-touch-icon.png" />
    <link rel="manifest" href="/favicon/site.webmanifest" />
    <meta name="apple-mobile-web-app-title" content="SaasCan" />
    <meta name="theme-color" content="#edf0f1" />

    <!-- Open Graph / Facebook -->
    <meta property="og:title" content="SaasCan - AI-Powered SaaS Idea Scanner & Analysis" />
    <meta
      property="og:description"
      content="Scan and analyze your SaaS ideas with AI-powered insights. Support for English and Arabic with advanced business recommendations."
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://saascan.vercel.app/" />
    <meta property="og:image" content="https://saascan.vercel.app/og-image.png" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="SaasCan - AI-Powered SaaS Idea Scanner & Analysis" />
    <meta
      name="twitter:description"
      content="Scan and analyze your SaaS ideas with AI-powered insights. Support for English and Arabic with advanced business recommendations."
    />
    <meta name="twitter:image" content="https://saascan.vercel.app/og-image.png" />

    <!-- Canonical -->
    <link rel="canonical" href="https://saascan.vercel.app/" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
